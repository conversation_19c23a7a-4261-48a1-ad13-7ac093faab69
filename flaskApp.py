from flask import Flask, redirect, url_for, render_template, request, session, flash, jsonify
import csv
import os
from datetime import timedelta, datetime
from functools import wraps
import bcrypt
import sqlite3
import json
from os import listdir
from os.path import isfile, join
from supabase import create_client, Client

app = Flask(__name__)
app.secret_key = '$4$4$4$6'  # Use a secure key for sessions
app.permanent_session_lifetime = timedelta(days=7)  # Session will last 7 days

# Initialize Supabase client with the provided credentials
SUPABASE_URL = "https://bqzxivznfjghzibjmync.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJxenhpdnpuZmpnaHppYmpteW5jIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODIzNDM0OSwiZXhwIjoyMDYzODEwMzQ5fQ.uN7BhumdpgsYOKvDSO-r9HrxRxmWddRHyPBt_i-s86A"
supabase = create_client(SUPABASE_URL, SUPABASE_KEY)

# Add a custom filter to parse JSO<PERSON> strings
@app.template_filter('fromjson')
def from_json(value):
    try:
        return json.loads(value)
    except:
        return None

# Simple credentials for your private use
USERNAME = 'Hedgepro44'
PASSWORD = '$4$4$4$6'
app.permanent_session_lifetime = timedelta(days=30)  # Session will last 30 days

DATABASE = "users.db"

def init_db():
    with sqlite3.connect(DATABASE) as conn:
        cursor = conn.cursor()
        cursor.execute('''CREATE TABLE IF NOT EXISTS users (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            username TEXT UNIQUE NOT NULL,
                            password TEXT NOT NULL,
                            subscription_expiry DATE
                          )''')
        conn.commit()

init_db()

# Login Required Decorator
def login_required(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        if 'user' in session:
            return func(*args, **kwargs)
        else:
            flash("Please log in first.")
            return redirect(url_for('login'))
    return wrapper

# Data Loading (Using Supabase for today's matches)
names = []
prevCounter = 0 
prevMatchesData = []
prevMatchesData_w = []

todayMatchesData = []
todayMatchesData_w = []

nextMatchesData = []
nextMatchesData_w = []

menRankedPlayers = []
femaleRankedPlayers = []

# Function to load today's matches from Supabase
def load_todays_matches_from_supabase():
    global todayMatchesData, todayMatchesData_w
    
    # Clear existing data
    todayMatchesData = []
    todayMatchesData_w = []
    
    try:
        '''
        Loading the men's data for todays matches from supabase
        '''
        
        print("Attempting to load men's matches from Supabase...")
        response = supabase.table('todays_matches').select('*').execute()
        
        if response.data:
            print(f"Loaded {len(response.data)} rows from todays_matches table")
            
            skip = False
            
            data = response.data
            data.sort(key=lambda x: list(x.values())[0])
            
            for i, row in enumerate(data):
                if skip:
                    skip = False
                    continue
                values = list(row.values())
                
                # Skip the ID (first value)
                values = values[1:]
                
                # Check if this is a header row
                if 'header' in values:
                    todayMatchesData.append(values)
                else:
                    if i+1 >= len(data):
                        print(f"Warning: Unexpected end of data at index {i}")
                        continue
                        
                    skip = True
                    match = []
                    row1 = list(data[i].values())[1:]
                    row2 = list(data[i+1].values())[1:]
                    
                    # Clean up None values
                    if row1[-1] == None:
                        row1.pop()
                    if row2[-1] == None:
                        row2.pop()
                        
                    for j, value in enumerate(row1):
                        if value == None:
                            row1[j] = "-"
                    for j, value in enumerate(row2):
                        if value == None:
                            row2[j] = "-"
                    
                    # Check for prediction data in either row
                    has_prediction = False
                    prediction_data = None

                    for item in row1 + row2:
                        if isinstance(item, str) and item.startswith('prediction:'):
                            has_prediction = True
                            try:
                                prediction_json = item.replace('prediction:', '', 1)
                                prediction_data = json.loads(prediction_json)
                                print(f"Found prediction data: {prediction_data}")
                            except Exception as e:
                                print(f"Error parsing prediction data: {e}")

                    match.append(row1)
                    match.append(row2)

                    # Always add prediction metadata as third element
                    if has_prediction and prediction_data:
                        match.append({"has_prediction": True, "prediction_data": prediction_data})
                        print(f"Added prediction data to match at index {len(todayMatchesData)}")
                    else:
                        match.append({"has_prediction": False, "prediction_data": None})
                    
                    todayMatchesData.append(match)
        else:
            print("No data found in todays_matches table")
        
        '''
        Loading the women's data for todays matches from supabase
        '''
        print("Attempting to load women's matches from Supabase...")
        response = supabase.table('todays_matches_w').select('*').execute()
        
        if response.data:
            print(f"Loaded {len(response.data)} rows from todays_matches_w table")
            
            skip_w = False
            
            data_w = response.data
            data_w.sort(key=lambda x: list(x.values())[0])
            
            for i, row in enumerate(data_w):
                if skip_w:
                    skip_w = False
                    continue
                
                values = list(row.values())
                
                # Skip the ID (first value)
                values = values[1:]
                
                # Check if this is a header row
                if 'header' in values:
                    todayMatchesData_w.append(values)
                else:
                    if i+1 >= len(data_w):
                        print(f"Warning: Unexpected end of data at index {i}")
                        continue
                    
                    skip_w = True
                    match_w = []
                    
                    row1 = list(data_w[i].values())[1:]
                    row2 = list(data_w[i+1].values())[1:]
                    
                    # Clean up None values
                    if row1[-1] == None:
                        row1.pop()
                    if row2[-1] == None:
                        row2.pop()
                        
                    for j, value in enumerate(row1):
                        if value == None:
                            row1[j] = "-"
                    for j, value in enumerate(row2):
                        if value == None:
                            row2[j] = "-"
                    
                    # Check for prediction data in either row
                    has_prediction = False
                    prediction_data = None

                    for item in row1 + row2:
                        if isinstance(item, str) and item.startswith('prediction:'):
                            has_prediction = True
                            try:
                                prediction_json = item.replace('prediction:', '', 1)
                                prediction_data = json.loads(prediction_json)
                                print(f"Found prediction data: {prediction_data}")
                            except Exception as e:
                                print(f"Error parsing prediction data: {e}")

                    match_w.append(row1)
                    match_w.append(row2)

                    # Always add prediction metadata as third element
                    if has_prediction and prediction_data:
                        match_w.append({"has_prediction": True, "prediction_data": prediction_data})
                        print(f"Added prediction data to women's match at index {len(todayMatchesData_w)}")
                    else:
                        match_w.append({"has_prediction": False, "prediction_data": None})
                    
                    todayMatchesData_w.append(match_w)
        else:
            print("No data found in todays_matches_w table")
        
    except Exception as e:
        print(f"Error loading data from Supabase: {str(e)}")
        import traceback
        traceback.print_exc()
        # Don't use flash outside of a request context
        # flash(f"Error loading match data: {str(e)}", "danger")

# Load today's matches from Supabase on startup
load_todays_matches_from_supabase()

# LOGIN ROUTE
@app.route("/login", methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        # Simple authentication using the hardcoded credentials
        if username == USERNAME and password == PASSWORD:
            session['user'] = username
            session.permanent = True
            flash("Login successful!", "success")
            return redirect(url_for('searchPlayer'))
        else:
            flash("Invalid username or password", "danger")

    return render_template('login.html')

# LOGOUT ROUTE
@app.route("/logout")
def logout():
    session.pop('user', None)
    flash("You have been logged out.", "info")
    return redirect(url_for('login'))

# Load other data from CSV files (unchanged for now)
with open("previousMatchesCounter") as file:
    prevCounter = int(file.read())

if prevCounter > 0:
    with open(f"./Previous_Matches/{prevCounter}.csv") as file:
        temp = []
        csvreader = csv.reader(file)
        for row in csvreader:
            temp.append(row)
        i = 0
        while i < (len(temp) - 1):
            if temp[i][-1] == "header":
                prevMatchesData.append(temp[i])
                i += 1
            else:
                prevMatchesData.append([ temp[i], temp[i+1] ])
                i += 2
                
    with open(f"./Previous_Matches/{prevCounter}_w.csv") as file:
        temp = []
        csvreader = csv.reader(file)
        for row in csvreader:
            temp.append(row)
        i = 0
        while i < (len(temp) - 1):
            if temp[i][-1] == "header":
                prevMatchesData_w.append(temp[i])
                i += 1
            else:
                prevMatchesData_w.append([ temp[i], temp[i+1] ])
                i += 2
        
names = [f for f in listdir("./FlashScore_database/") if not isfile(join("./FlashScore_database/", f))]

# Load next matches from CSV (unchanged for now)
with open("nextMatches.csv") as file:
    temp = []
    csvreader = csv.reader(file)
    for row in csvreader:
        temp.append(row)
    i = 0
    while i < (len(temp) - 1):
        if temp[i][-1] == "header":
            nextMatchesData.append(temp[i])
            i += 1
        else:
            nextMatchesData.append([ temp[i], temp[i+1] ])
            i += 2

with open("nextMatches_w.csv") as file:
    temp = []
    csvreader = csv.reader(file)
    for row in csvreader:
        temp.append(row)
    i = 0
    while i < (len(temp) - 1):
        if temp[i][-1] == "header":
            nextMatchesData_w.append(temp[i])
            i += 1
        else:
            nextMatchesData_w.append([ temp[i], temp[i+1] ])
            i += 2

# Load rankings data (unchanged)
with open("men_ranking.csv") as file:
    csvreader = csv.reader(file)
    for row in csvreader:
        menRankedPlayers.append(row)
        
with open("women_ranking.csv") as file:
    csvreader = csv.reader(file)
    for row in csvreader:
        femaleRankedPlayers.append(row)

# Update the reload_data route to reload only from Supabase
@app.route("/reload_data")
@login_required
def reload_data():
    """Force reload of today's match data from Supabase"""
    load_todays_matches_from_supabase()
    flash("Today's matches data refreshed from Supabase!", "success")
    return redirect(url_for('searchPlayer'))

# Helper function to validate data structure
def validate_match_data(data, label):
    print(f"Validating {label} data structure...")
    
    if not data:
        print(f"{label} is empty")
        return
    
    for i, item in enumerate(data):
        if isinstance(item, list):
            if len(item) == 1 and item[0][-1] == "header":
                print(f"{label}[{i}] is a header row")
            elif len(item) >= 2:
                print(f"{label}[{i}] is a match with {len(item)} elements")
                
                # Check player rows
                for j, player in enumerate(item[:2]):
                    if not isinstance(player, list):
                        print(f"WARNING: {label}[{i}][{j}] is not a list: {player}")
                    elif len(player) < 9:
                        print(f"WARNING: {label}[{i}][{j}] has fewer than 9 elements: {player}")
                
                # Check prediction data
                if len(item) > 2:
                    if not isinstance(item[2], dict):
                        print(f"WARNING: {label}[{i}][2] is not a dict: {item[2]}")
                    elif "has_prediction" not in item[2]:
                        print(f"WARNING: {label}[{i}][2] missing 'has_prediction' key: {item[2]}")
            else:
                print(f"WARNING: {label}[{i}] has unexpected structure: {item}")
        else:
            print(f"WARNING: {label}[{i}] is not a list: {item}")

# Helper function to print data structure for debugging
def print_data_structure(data, label):
    """Print the structure of a data object for debugging"""
    print(f"\n--- {label} Structure ---")
    print(f"Type: {type(data)}")
    print(f"Length: {len(data)}")
    
    if not data:
        print("Empty data")
        return
    
    # Print first few items
    for i, item in enumerate(data[:3]):
        print(f"\nItem {i}:")
        print(f"  Type: {type(item)}")
        
        if isinstance(item, list):
            print(f"  Length: {len(item)}")
            if len(item) > 0:
                if isinstance(item[0], list):
                    print(f"  First element: List with {len(item[0])} items")
                    if len(item[0]) > 0:
                        print(f"    First value: {item[0][0]}")
                else:
                    print(f"  First element: {item[0]}")
        else:
            print(f"  Value: {item}")
    
    print(f"... {len(data) - 3} more items\n")

# PLAYER SEARCH PAGE (Login Required)
@app.route("/", methods=['GET', 'POST'])
@login_required
def searchPlayer():
    print("=== SEARCH PLAYER ROUTE CALLED ===")
    # Declare globals at the beginning of the function
    global todayMatchesData, todayMatchesData_w
    global nextMatchesData, nextMatchesData_w
    global prevMatchesData, prevMatchesData_w
    
    # Reload data from Supabase if needed
    if not todayMatchesData or not todayMatchesData_w:
        print("No today's match data found, loading from Supabase...")
        load_todays_matches_from_supabase()
    
    # Print data structure for debugging
    print_data_structure(todayMatchesData, "todayMatchesData")
    print_data_structure(todayMatchesData_w, "todayMatchesData_w")
    
    # Handle form submission
    if request.method == "POST":
        player_name = request.form.get("player_name")
        if player_name != "":
            return redirect(url_for('player_profile_page', name=player_name.lower()))
    
    # Pass the data to the template
    return render_template('home.html', 
                          today_rows=todayMatchesData, 
                          next_rows=nextMatchesData, 
                          prev_rows=prevMatchesData, 
                          today_rows_w=todayMatchesData_w, 
                          next_rows_w=nextMatchesData_w, 
                          prev_rows_w=prevMatchesData_w)

# RANKED PLAYERS PAGE (Login Required)
@app.route("/ranked-players")
@login_required
def rankedPlayers():
    return render_template("ranked_players.html", men_data=menRankedPlayers, female_data=femaleRankedPlayers)

def upload_prediction_to_supabase(prediction_string, match_index, is_women):
    """Upload only the prediction data to specific rows in Supabase"""
    try:
        # Determine which table to use
        table_name = "todays_matches_w" if is_women else "todays_matches"

        print(f"Uploading prediction to {table_name} table for match index {match_index}...")

        # Get all rows from the table
        response = supabase.table(table_name).select('*').execute()
        if not response.data:
            return False, "No data found in table"

        all_rows = response.data
        print(f"Found {len(all_rows)} total rows in {table_name}")

        # Find the rows that correspond to this match
        # Skip header rows and count actual match rows
        actual_match_count = 0
        target_row_indices = []

        for i, row in enumerate(all_rows):
            # Check if this is a header row (contains "Time" in first column)
            first_col_key = list(row.keys())[1] if len(row.keys()) > 1 else None  # Skip 'id' column
            if first_col_key and row.get(first_col_key) == "Time":
                continue  # Skip header row

            # This is a match row
            if actual_match_count == match_index * 2:  # Player 1 row
                target_row_indices.append(i)
            elif actual_match_count == match_index * 2 + 1:  # Player 2 row
                target_row_indices.append(i)
                break  # We found both rows for this match

            actual_match_count += 1

        if len(target_row_indices) != 2:
            return False, f"Could not find both player rows for match index {match_index}"

        print(f"Found target rows at indices: {target_row_indices}")

        # Update both rows with the prediction string
        success_count = 0
        for row_index in target_row_indices:
            target_row = all_rows[row_index]
            row_id = target_row['id']

            # Check if there's already a prediction in any column
            existing_prediction_column = None
            for col, value in target_row.items():
                if col != 'id' and isinstance(value, str) and value.startswith('prediction:'):
                    existing_prediction_column = col
                    break

            if existing_prediction_column:
                # Update existing prediction column
                update_column = existing_prediction_column
                print(f"Updating existing prediction in row {row_id} column '{update_column}'")
            else:
                # Use the "Header" column for predictions (as requested)
                available_columns = list(target_row.keys())
                print(f"Available columns: {available_columns}")

                # Use the "Header" column specifically
                if 'Header' in available_columns:
                    update_column = 'Header'
                    print(f"Using 'Header' column for prediction in row {row_id}")
                else:
                    print(f"'Header' column not found in row {row_id}. Available columns: {available_columns}")
                    # Fallback to other columns if Header doesn't exist
                    if 'A' in available_columns:
                        update_column = 'A'
                        print(f"Fallback: Using column 'A' for prediction in row {row_id}")
                    else:
                        # Use the last available column (excluding 'id')
                        non_id_columns = [col for col in available_columns if col != 'id']
                        if non_id_columns:
                            update_column = non_id_columns[-1]
                            print(f"Fallback: Using last available column '{update_column}' for prediction in row {row_id}")
                        else:
                            print(f"No suitable column found for prediction in row {row_id}")
                            continue

            # Update the row with the prediction
            update_data = {update_column: prediction_string}
            update_result = supabase.table(table_name).update(update_data).eq('id', row_id).execute()

            if update_result.data:
                success_count += 1
                print(f"Successfully updated row {row_id}")
            else:
                print(f"Failed to update row {row_id}")

        if success_count == 2:
            print(f"Successfully updated both rows for match {match_index}")

            # Reload the data from Supabase to refresh the in-memory data
            load_todays_matches_from_supabase()

            return True, "Prediction saved successfully to database"
        else:
            return False, f"Only updated {success_count} out of 2 rows"

    except Exception as e:
        print(f"Error uploading prediction to Supabase: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, f"Upload error: {str(e)}"

def update_supabase_with_prediction(prediction_data, match_index, is_women):
    """Update Supabase database directly with prediction data for today's matches"""
    try:
        # Determine which table to use
        table_name = "todays_matches_w" if is_women else "todays_matches"

        print(f"Updating {table_name} table with prediction for match index {match_index}")

        # Get all rows from the table
        all_rows = supabase.table(table_name).select('*').execute()

        if not all_rows.data:
            print("No data found in Supabase table")
            return False, "No match data found in database"

        # Find the match rows by counting actual matches (skip headers)
        actual_match_count = 0
        target_player1_row = None
        target_player2_row = None

        i = 0
        while i < len(all_rows.data):
            current_row = all_rows.data[i]

            # Skip header rows (they have specific markers)
            if current_row.get('Name', '').endswith('header') or 'header' in str(current_row.get('Name', '')):
                i += 1
                continue

            # Check if this is a player1 row (followed by a player2 row)
            if i + 1 < len(all_rows.data):
                next_row = all_rows.data[i + 1]

                # Skip if next row is also a header
                if next_row.get('Name', '').endswith('header') or 'header' in str(next_row.get('Name', '')):
                    i += 1
                    continue

                # This is a match (player1 + player2)
                if actual_match_count == match_index:
                    target_player1_row = current_row
                    target_player2_row = next_row
                    print(f"Found target match: {target_player1_row.get('Name')} vs {target_player2_row.get('Name')}")
                    break

                actual_match_count += 1
                i += 2  # Skip both player rows
            else:
                i += 1

        if not target_player1_row or not target_player2_row:
            print(f"Could not find match at index {match_index}")
            return False, "Match not found in database"

        # Update both player rows with the prediction
        player1_id = target_player1_row.get('id')
        player2_id = target_player2_row.get('id')

        print(f"Updating player rows with IDs: {player1_id}, {player2_id}")

        # Debug: Print available columns
        print(f"Available columns in player1 row: {list(target_player1_row.keys())}")
        print(f"Available columns in player2 row: {list(target_player2_row.keys())}")

        # Find the correct column to update - look for any available column that's not 'id' or critical data
        # Get all available columns and find one that can store our prediction
        available_columns = list(target_player1_row.keys())
        print(f"All available columns: {available_columns}")

        # Exclude critical columns that shouldn't be overwritten
        excluded_columns = ['id', 'Date&Time', 'Name']

        # Try to find a column that's either empty or contains minimal data
        update_column = None
        for col in available_columns:
            if col not in excluded_columns:
                current_value = target_player1_row.get(col)
                # Prefer empty columns or columns with short values
                if not current_value or (isinstance(current_value, str) and len(current_value) < 10):
                    update_column = col
                    print(f"Found suitable column to update: {col} (current value: '{current_value}')")
                    break

        # If no empty column found, use the last available non-excluded column
        if not update_column:
            for col in reversed(available_columns):
                if col not in excluded_columns:
                    update_column = col
                    print(f"Using column as fallback: {col}")
                    break

        if not update_column:
            print("No suitable column found to store prediction")
            return False, "No suitable column found to store prediction"

        # Create a proper JSON prediction string that matches what the loading function expects
        prediction_json = json.dumps(prediction_data)
        compact_prediction = f"prediction:{prediction_json}"

        # Ensure it's under database limits (if any)
        if len(compact_prediction) > 500:  # Increased limit for JSON
            # If too long, create a shorter version
            short_prediction = {
                "winner": prediction_data['winner'],
                "player1_score": prediction_data.get('player1_score', ''),
                "player2_score": prediction_data.get('player2_score', ''),
                "spread": prediction_data.get('spread', ''),
                "notes": prediction_data.get('notes', '')[:50] if prediction_data.get('notes') else ''  # Truncate notes
            }
            prediction_json = json.dumps(short_prediction)
            compact_prediction = f"prediction:{prediction_json}"

        print(f"Using prediction string: {compact_prediction}")

        # Update player 1 row
        update_result1 = supabase.table(table_name).update({update_column: compact_prediction}).eq('id', player1_id).execute()
        print(f"Update result for player1: {update_result1}")

        # Update player 2 row
        update_result2 = supabase.table(table_name).update({update_column: compact_prediction}).eq('id', player2_id).execute()
        print(f"Update result for player2: {update_result2}")

        # Reload data from Supabase to refresh the in-memory data
        load_todays_matches_from_supabase()

        return True, "Prediction saved successfully to database"

    except Exception as e:
        print(f"Error updating Supabase with prediction: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, f"Database error: {str(e)}"

# Add this route to handle saving predictions
@app.route("/save_prediction", methods=['POST'])
@login_required
def save_prediction():
    try:
        # Declare globals at the beginning of the function
        global todayMatchesData, todayMatchesData_w
        global nextMatchesData, nextMatchesData_w

        # Get data from request
        data = request.json
        match_index = int(data.get('match_index'))
        is_women = data.get('is_women') == True
        is_next_day = data.get('is_next_day') == True
        winner = data.get('winner', '')
        player1_score = data.get('player1_score', '')
        player2_score = data.get('player2_score', '')
        spread = data.get('spread', '')
        notes = data.get('notes', '')

        # Print debug information
        print(f"Saving prediction: match_index={match_index}, is_women={is_women}, is_next_day={is_next_day}, winner={winner}")
        print(f"Scores: player1={player1_score}, player2={player2_score}, spread={spread}")

        # Validate required fields
        if not winner:
            return jsonify({"success": False, "error": "Winner is required"})

        # Format prediction data as a JSON object (same as reference implementation)
        prediction_data = {
            "winner": winner,
            "player1_score": player1_score,
            "player2_score": player2_score,
            "spread": spread,
            "notes": notes
        }

        # Convert to string format: "prediction:JSON_DATA" (exactly same as reference implementation)
        prediction_string = f"prediction:{json.dumps(prediction_data)}"
        print(f"Generated prediction string: {prediction_string}")

        # Get the correct in-memory data based on gender and day
        if is_next_day:
            if is_women:
                matches_data = nextMatchesData_w
            else:
                matches_data = nextMatchesData
        else:
            if is_women:
                matches_data = todayMatchesData_w
            else:
                matches_data = todayMatchesData

        # Find the match in the in-memory data and add prediction string to both player rows
        actual_match_count = 0
        target_match_index = None

        for i, item in enumerate(matches_data):
            # Skip header rows
            if isinstance(item, list) and len(item) == 1 and item[0][-1] == "header":
                continue

            # This is a match
            if actual_match_count == match_index:
                target_match_index = i
                break

            actual_match_count += 1

        if target_match_index is None:
            return jsonify({"success": False, "error": f"Match with index {match_index} not found"})

        # Get the match data
        match_data = matches_data[target_match_index]

        if len(match_data) < 2:
            return jsonify({"success": False, "error": "Invalid match data structure"})

        # Remove any existing prediction from both player rows
        player1_row = [item for item in match_data[0] if not (isinstance(item, str) and item.startswith('prediction:'))]
        player2_row = [item for item in match_data[1] if not (isinstance(item, str) and item.startswith('prediction:'))]

        # Add the new prediction string to both player rows
        player1_row.append(prediction_string)
        player2_row.append(prediction_string)

        # Update the match data
        match_data[0] = player1_row
        match_data[1] = player2_row

        # Update or add the prediction metadata (third element)
        if len(match_data) > 2:
            match_data[2] = {"has_prediction": True, "prediction_data": prediction_data}
        else:
            match_data.append({"has_prediction": True, "prediction_data": prediction_data})

        print(f"Updated match data: Player1 row length: {len(match_data[0])}, Player2 row length: {len(match_data[1])}")

        # For today's matches: Upload prediction to Supabase
        if not is_next_day:
            upload_success, upload_message = upload_prediction_to_supabase(prediction_string, match_index, is_women)

            if not upload_success:
                return jsonify({"success": False, "error": f"Failed to upload to Supabase: {upload_message}"})

            return jsonify({
                "success": True,
                "message": "Prediction saved successfully to database!",
                "reload_needed": True
            })

        # For next day matches: Update CSV files (keep existing functionality)
        else:
            csv_success = update_csv_with_prediction(prediction_data, match_index, is_women, is_next_day)

            if not csv_success:
                return jsonify({"success": False, "error": "Failed to save prediction to file"})

            return jsonify({
                "success": True,
                "message": "Prediction saved successfully!",
                "reload_needed": True
            })

    except Exception as e:
        print(f"Error saving prediction: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "error": str(e)})

def update_csv_with_prediction(prediction_data, match_index, is_women, is_next_day):
    """Update CSV files with prediction data (secondary operation)"""
    try:
        # Convert to string format: "prediction:JSON_DATA"
        prediction_string = f"prediction:{json.dumps(prediction_data)}"

        # Determine which file to use
        if is_next_day:
            file_name = "nextMatches_w.csv" if is_women else "nextMatches.csv"
        else:
            file_name = "todaysMatches_w.csv" if is_women else "todaysMatches.csv"

        # Read the current CSV file
        with open(file_name, 'r') as file:
            csvreader = csv.reader(file)
            rows = list(csvreader)

        # Find the match rows
        actual_match_count = 0
        row1_index = None
        row2_index = None

        i = 0
        while i < len(rows):
            # Skip empty rows
            if not rows[i] or len(rows[i]) == 0:
                i += 1
                continue

            # Skip header rows
            if rows[i][-1] == "header":
                i += 1
                continue

            # Check if this is a player1 row (followed by a player2 row)
            if (i+1 < len(rows) and
                len(rows[i]) > 0 and
                len(rows[i+1]) > 0):

                # This is a match (player1 + player2)
                if actual_match_count == match_index:
                    # This is the match we want to update
                    row1_index = i
                    row2_index = i+1
                    break

                actual_match_count += 1
                i += 2  # Skip both player rows
            else:
                i += 1

        # Add the new prediction to both player rows
        if row1_index is not None and row2_index is not None:
            # Remove any existing prediction
            rows[row1_index] = [item for item in rows[row1_index] if not (isinstance(item, str) and item.startswith('prediction:'))]
            rows[row2_index] = [item for item in rows[row2_index] if not (isinstance(item, str) and item.startswith('prediction:'))]

            # Add the new prediction
            rows[row1_index].append(prediction_string)
            rows[row2_index].append(prediction_string)

            # Write back to the file
            with open(file_name, 'w', newline='') as file:
                csvwriter = csv.writer(file)
                csvwriter.writerows(rows)

            print(f"Updated CSV file {file_name} with prediction")
            return True
        else:
            print(f"Could not find match rows in CSV file {file_name}")
            return False

    except Exception as e:
        print(f"Error updating CSV with prediction: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# Function to reupload today's matches to Supabase
def reupload_todays_matches_to_supabase(is_women=False):
    """Reupload today's matches data to Supabase"""
    try:
        file_name = "todaysMatches_w.csv" if is_women else "todaysMatches.csv"
        table_name = "todays_matches_w" if is_women else "todays_matches"
        
        # Read the CSV file
        rows = []
        with open(file_name, 'r') as file:
            csvreader = csv.reader(file)
            rows = list(csvreader)
        
        # Clear the table first
        supabase.table(table_name).delete().neq('id', 0).execute()
        
        # Insert rows one by one
        for i, row in enumerate(rows):
            # Skip empty rows
            if not row:
                continue
                
            # Create a dictionary for the row
            row_dict = {}
            
            # Add each column with a generic name
            for j, value in enumerate(row):
                if j == 0:
                    row_dict["Date&Time"] = value
                elif j == 1:
                    row_dict["Name"] = value
                else:
                    row_dict[f"Column{j}"] = value
            
            # Insert the row
            supabase.table(table_name).insert(row_dict).execute()
            
        print(f"Reuploaded {len(rows)} rows to {table_name}")
        
        # Reload the data from Supabase
        load_todays_matches_from_supabase()
        
        return True
    except Exception as e:
        print(f"Error reuploading data to Supabase: {str(e)}")
        import traceback
        traceback.print_exc()
        return False



# Single route for reuploading today's matches
@app.route("/reupload_todays_matches")
@login_required
def reupload_todays_matches():
    """Force reupload of today's match data to Supabase"""
    men_result = reupload_todays_matches_to_supabase(is_women=False)
    women_result = reupload_todays_matches_to_supabase(is_women=True)

    if men_result and women_result:
        flash("Today's matches data reuploaded to Supabase successfully!", "success")
    else:
        flash("Error reuploading today's matches data to Supabase.", "danger")

    return redirect(url_for('searchPlayer'))

# Add this route to force reload data from files
@app.route("/refresh_data")
@login_required
def refresh_data():
    """Force reload of all match data from CSV files"""
    global todayMatchesData, nextMatchesData, prevMatchesData
    global todayMatchesData_w, nextMatchesData_w, prevMatchesData_w
    global menRankedPlayers, femaleRankedPlayers
    
    # Reload today's matches (men)
    todayMatchesData = []
    with open("todaysMatches.csv") as file:
        temp = []
        csvreader = csv.reader(file)
        for row in csvreader:
            temp.append(row)
        i = 0
        while i < len(temp):
            if not temp[i] or len(temp[i]) == 0:
                i += 1
                continue
                
            if temp[i][-1] == "header":
                todayMatchesData.append(temp[i])
                i += 1
            elif i+1 < len(temp):
                # Check for prediction data in either row
                has_prediction = False
                prediction_data = None
                
                for item in temp[i] + (temp[i+1] if i+1 < len(temp) else []):
                    if isinstance(item, str) and item.startswith('prediction:'):
                        has_prediction = True
                        try:
                            prediction_json = item.replace('prediction:', '', 1)
                            prediction_data = json.loads(prediction_json)
                        except:
                            pass
                
                match_data = [temp[i], temp[i+1]]
                
                # Add prediction data if found
                if has_prediction and prediction_data:
                    match_data.append({"has_prediction": True, "prediction_data": prediction_data})
                
                todayMatchesData.append(match_data)
                i += 2
            else:
                i += 1

    # Reload women's today matches
    todayMatchesData_w = []
    with open("todaysMatches_w.csv") as file:
        temp = []
        csvreader = csv.reader(file)
        for row in csvreader:
            temp.append(row)
        i = 0
        while i < len(temp):
            if not temp[i] or len(temp[i]) == 0:
                i += 1
                continue
                
            if temp[i][-1] == "header":
                todayMatchesData_w.append(temp[i])
                i += 1
            elif i+1 < len(temp):
                # Check for prediction data in either row
                has_prediction = False
                prediction_data = None
                
                for item in temp[i] + (temp[i+1] if i+1 < len(temp) else []):
                    if isinstance(item, str) and item.startswith('prediction:'):
                        has_prediction = True
                        try:
                            prediction_json = item.replace('prediction:', '', 1)
                            prediction_data = json.loads(prediction_json)
                        except:
                            pass
                
                match_data = [temp[i], temp[i+1]]
                
                # Add prediction data if found
                if has_prediction and prediction_data:
                    match_data.append({"has_prediction": True, "prediction_data": prediction_data})
                
                todayMatchesData_w.append(match_data)
                i += 2

    # Reload next matches (men)
    nextMatchesData = []
    with open("nextMatches.csv") as file:
        temp = []
        csvreader = csv.reader(file)
        for row in csvreader:
            temp.append(row)
        i = 0
        while i < (len(temp) - 1):
            if temp[i][-1] == "header":
                nextMatchesData.append(temp[i])
                i += 1
            else:
                nextMatchesData.append([ temp[i], temp[i+1] ])
                i += 2

    # Reload next matches (women)
    nextMatchesData_w = []
    with open("nextMatches_w.csv") as file:
        temp = []
        csvreader = csv.reader(file)
        for row in csvreader:
            temp.append(row)
        i = 0
        while i < (len(temp) - 1):
            if temp[i][-1] == "header":
                nextMatchesData_w.append(temp[i])
                i += 1
            else:
                nextMatchesData_w.append([ temp[i], temp[i+1] ])
                i += 2

    # Reload previous matches if counter > 0
    prevMatchesData = []
    prevMatchesData_w = []
    with open("previousMatchesCounter") as file:
        prevCounter = int(file.read())
    
    if prevCounter > 0:
        with open(f"./Previous_Matches/{prevCounter}.csv") as file:
            temp = []
            csvreader = csv.reader(file)
            for row in csvreader:
                temp.append(row)
            i = 0
            while i < (len(temp) - 1):
                if temp[i][-1] == "header":
                    prevMatchesData.append(temp[i])
                    i += 1
                else:
                    prevMatchesData.append([ temp[i], temp[i+1] ])
                    i += 2
                    
        with open(f"./Previous_Matches/{prevCounter}_w.csv") as file:
            temp = []
            csvreader = csv.reader(file)
            for row in csvreader:
                temp.append(row)
            i = 0
            while i < (len(temp) - 1):
                if temp[i][-1] == "header":
                    prevMatchesData_w.append(temp[i])
                    i += 1
                else:
                    prevMatchesData_w.append([ temp[i], temp[i+1] ])
                    i += 2

    flash("Data refreshed successfully!", "success")
    return redirect(url_for('searchPlayer'))

# Add a diagnostic route to check Supabase connection and data
@app.route("/check_supabase")
@login_required
def check_supabase():
    try:
        # Check connection to Supabase
        tables_response = supabase.table('todays_matches').select('*').limit(1).execute()
        
        # Get table structure
        men_structure = {}
        women_structure = {}
        
        # Get a sample row from men's table
        men_response = supabase.table('todays_matches').select('*').limit(1).execute()
        if men_response.data and len(men_response.data) > 0:
            men_structure = men_response.data[0]
        
        # Get a sample row from women's table
        women_response = supabase.table('todays_matches_w').select('*').limit(1).execute()
        if women_response.data and len(women_response.data) > 0:
            women_structure = women_response.data[0]
        
        # Count rows in each table
        men_count_response = supabase.table('todays_matches').select('count', count='exact').execute()
        women_count_response = supabase.table('todays_matches_w').select('count', count='exact').execute()
        
        men_count = men_count_response.count if hasattr(men_count_response, 'count') else 'Unknown'
        women_count = women_count_response.count if hasattr(women_count_response, 'count') else 'Unknown'
        
        # Force reload data
        load_todays_matches_from_supabase()
        
        # Return diagnostic information
        return jsonify({
            "connection": "success",
            "men_table_count": men_count,
            "women_table_count": women_count,
            "men_sample_structure": men_structure,
            "women_sample_structure": women_structure,
            "todayMatchesData_length": len(todayMatchesData),
            "todayMatchesData_w_length": len(todayMatchesData_w)
        })
    except Exception as e:
        return jsonify({
            "connection": "failed",
            "error": str(e)
        })

# Add a debug route to view the data structure
@app.route("/debug_data")
@login_required
def debug_data():
    """Debug route to view the data structure"""
    global todayMatchesData, todayMatchesData_w
    global nextMatchesData, nextMatchesData_w
    global prevMatchesData, prevMatchesData_w
    
    # Reload data from Supabase
    load_todays_matches_from_supabase()
    
    # Get raw data from Supabase for comparison
    men_raw = []
    women_raw = []
    
    try:
        men_response = supabase.table('todays_matches').select('*').order('id').execute()
        if men_response.data:
            men_raw = men_response.data
            
        women_response = supabase.table('todays_matches_w').select('*').order('id').execute()
        if women_response.data:
            women_raw = women_response.data
    except Exception as e:
        print(f"Error getting raw data: {e}")
    
    # Return debug information
    return jsonify({
        "todayMatchesData_length": len(todayMatchesData),
        "todayMatchesData_w_length": len(todayMatchesData_w),
        "nextMatchesData_length": len(nextMatchesData),
        "nextMatchesData_w_length": len(nextMatchesData_w),
        "prevMatchesData_length": len(prevMatchesData),
        "prevMatchesData_w_length": len(prevMatchesData_w),
        "men_raw_sample": men_raw[:2] if men_raw else [],
        "women_raw_sample": women_raw[:2] if women_raw else [],
        "todayMatchesData_sample": todayMatchesData[:2] if todayMatchesData else [],
        "todayMatchesData_w_sample": todayMatchesData_w[:2] if todayMatchesData_w else []
    })

# Add a debug route to view the raw data from Supabase
@app.route("/debug_raw_data")
@login_required
def debug_raw_data():
    """Debug route to view the raw data from Supabase"""
    try:
        # Get raw data from Supabase
        men_response = supabase.table('todays_matches').select('*').order('id').execute()
        women_response = supabase.table('todays_matches_w').select('*').order('id').execute()
        
        men_data = men_response.data if men_response.data else []
        women_data = women_response.data if women_response.data else []
        
        # Count headers and matches
        men_headers = [row for row in men_data if row.get('is_header', False)]
        women_headers = [row for row in women_data if row.get('is_header', False)]
        
        men_match_ids = set(row.get('match_id') for row in men_data if row.get('match_id'))
        women_match_ids = set(row.get('match_id') for row in women_data if row.get('match_id'))
        
        # Return debug information
        return jsonify({
            "men_data_count": len(men_data),
            "women_data_count": len(women_data),
            "men_headers_count": len(men_headers),
            "women_headers_count": len(women_headers),
            "men_match_ids_count": len(men_match_ids),
            "women_match_ids_count": len(women_match_ids),
            "men_sample": men_data[:5] if men_data else [],
            "women_sample": women_data[:5] if women_data else []
        })
    except Exception as e:
        return jsonify({
            "error": str(e)
        })

# Add a route to check the structure of the data in Supabase
@app.route("/check_supabase_structure")
@login_required
def check_supabase_structure():
    """Check the structure of the data in Supabase"""
    try:
        # Get the first row from each table
        men_response = supabase.table('todays_matches').select('*').limit(1).execute()
        women_response = supabase.table('todays_matches_w').select('*').limit(1).execute()
        
        # Get all column names
        men_columns = []
        women_columns = []
        
        if men_response.data and len(men_response.data) > 0:
            men_columns = list(men_response.data[0].keys())
        
        if women_response.data and len(women_response.data) > 0:
            women_columns = list(women_response.data[0].keys())
        
        # Get a few sample rows
        men_sample = supabase.table('todays_matches').select('*').limit(5).execute()
        women_sample = supabase.table('todays_matches_w').select('*').limit(5).execute()
        
        # Return the structure information
        return jsonify({
            "men_columns": men_columns,
            "women_columns": women_columns,
            "men_sample": men_sample.data if men_sample.data else [],
            "women_sample": women_sample.data if women_sample.data else []
        })
    except Exception as e:
        return jsonify({
            "error": str(e)
        })

# Add a debug route to check the prediction data
@app.route("/debug_predictions")
@login_required
def debug_predictions():
    """Debug route to check prediction data"""
    global todayMatchesData, todayMatchesData_w
    
    # Count matches with predictions
    men_predictions = 0
    women_predictions = 0
    
    men_prediction_samples = []
    women_prediction_samples = []
    
    # Check men's matches
    for i, item in enumerate(todayMatchesData):
        if isinstance(item, list) and len(item) > 2:
            men_predictions += 1
            if len(men_prediction_samples) < 2:
                men_prediction_samples.append({
                    "match_index": i,
                    "prediction_data": item[2]
                })
    
    # Check women's matches
    for i, item in enumerate(todayMatchesData_w):
        if isinstance(item, list) and len(item) > 2:
            women_predictions += 1
            if len(women_prediction_samples) < 2:
                women_prediction_samples.append({
                    "match_index": i,
                    "prediction_data": item[2]
                })
    
    # Check raw data in Supabase
    men_raw_predictions = []
    women_raw_predictions = []
    
    try:
        # Check men's table
        men_response = supabase.table('todays_matches').select('*').execute()
        if men_response.data:
            for row in men_response.data:
                for key, value in row.items():
                    if isinstance(value, str) and value.startswith('prediction:'):
                        men_raw_predictions.append({
                            "id": row.get('id'),
                            "prediction": value
                        })
                        break
        
        # Check women's table
        women_response = supabase.table('todays_matches_w').select('*').execute()
        if women_response.data:
            for row in women_response.data:
                for key, value in row.items():
                    if isinstance(value, str) and value.startswith('prediction:'):
                        women_raw_predictions.append({
                            "id": row.get('id'),
                            "prediction": value
                        })
                        break
    except Exception as e:
        print(f"Error checking raw predictions: {e}")
    
    return jsonify({
        "men_matches_total": len([item for item in todayMatchesData if isinstance(item, list) and len(item) >= 2 and not (len(item) == 1 and 'header' in item[0])]),
        "women_matches_total": len([item for item in todayMatchesData_w if isinstance(item, list) and len(item) >= 2 and not (len(item) == 1 and 'header' in item[0])]),
        "men_matches_with_predictions": men_predictions,
        "women_matches_with_predictions": women_predictions,
        "men_prediction_samples": men_prediction_samples,
        "women_prediction_samples": women_prediction_samples,
        "men_raw_predictions": men_raw_predictions[:5],
        "women_raw_predictions": women_raw_predictions[:5]
    })

# Add a test route to manually test prediction saving
@app.route("/test_prediction_save")
@login_required
def test_prediction_save():
    """Test route to manually save a prediction"""
    print("=== TEST PREDICTION SAVE ROUTE CALLED ===")
    try:
        # Test data
        test_prediction = {
            "winner": "player1",
            "player1_score": "2",
            "player2_score": "1",
            "spread": "1.5",
            "notes": "Test prediction"
        }

        print(f"Test prediction data: {test_prediction}")

        # Try to save to the first match (index 0) in men's data
        success, message = update_supabase_with_prediction(test_prediction, 0, False)

        print(f"Update result: success={success}, message={message}")

        return jsonify({
            "success": success,
            "message": message,
            "test_data": test_prediction
        })
    except Exception as e:
        print(f"Error in test route: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "error": str(e)
        })

# Add a function to store predictions in a separate table
def store_prediction_in_separate_table(prediction_data, match_info, is_women=False):
    """Store prediction in a separate predictions table for better tracking"""
    try:
        # Create a predictions table if it doesn't exist
        table_name = "predictions"
        
        # Format the prediction data
        player1_name = match_info[0][1] if len(match_info[0]) > 1 else "Unknown"
        player2_name = match_info[1][1] if len(match_info[1]) > 1 else "Unknown"
        match_date = match_info[0][0] if len(match_info[0]) > 0 else "Unknown"
        tournament = "Unknown"
        
        # Try to extract tournament info from header rows
        for item in (todayMatchesData_w if is_women else todayMatchesData):
            if isinstance(item, list) and len(item) == 1 and 'header' in item[0]:
                tournament = item[0].split(',')[0]
                break
        
        # Insert the prediction
        prediction_record = {
            "match_date": match_date,
            "tournament": tournament,
            "player1": player1_name,
            "player2": player2_name,
            "predicted_winner": player1_name if prediction_data["winner"] == "player1" else player2_name,
            "predicted_score": f"{prediction_data['player1_score']}-{prediction_data['player2_score']}",
            "spread": prediction_data["spread"],
            "notes": prediction_data["notes"],
            "is_women": is_women,
            "created_at": datetime.now().isoformat()
        }
        
        # Insert into Supabase
        result = supabase.table(table_name).insert(prediction_record).execute()
        print(f"Stored prediction in separate table: {result}")
        
        return True
    except Exception as e:
        print(f"Error storing prediction in separate table: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# Simple test route without login requirement
@app.route("/test")
def test():
    print("=== TEST ROUTE CALLED ===")
    return "Test route working!"

# RUNNING THE APP
if __name__ == "__main__":
    app.run(debug=True)
